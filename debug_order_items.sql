-- 调试订单项问题的SQL脚本
USE mall;

-- 1. 检查数据库和表结构
SELECT 'Database and table check:' as step;
SELECT DATABASE() as current_database;
SHOW TABLES LIKE '%order%';

-- 2. 检查 order_items 表是否存在
SELECT 'Checking order_items table:' as step;
SELECT COUNT(*) as table_exists 
FROM information_schema.tables 
WHERE table_schema = 'mall' AND table_name = 'order_items';

-- 3. 如果表存在，显示表结构
SELECT 'order_items table structure:' as step;
DESCRIBE order_items;

-- 4. 检查最近的订单
SELECT 'Recent orders:' as step;
SELECT id, user_id, order_code, total_amount, created_at 
FROM orders 
WHERE user_id = 4 
ORDER BY created_at DESC 
LIMIT 5;

-- 5. 检查这些订单是否有对应的订单项
SELECT 'Order items for user 4 orders:' as step;
SELECT oi.*, o.order_code, o.created_at as order_created
FROM order_items oi 
RIGHT JOIN orders o ON oi.order_id = o.id 
WHERE o.user_id = 4 
ORDER BY o.created_at DESC 
LIMIT 10;

-- 6. 统计数据
SELECT 'Data statistics:' as step;
SELECT 
  (SELECT COUNT(*) FROM orders WHERE user_id = 4) as total_orders,
  (SELECT COUNT(*) FROM order_items oi JOIN orders o ON oi.order_id = o.id WHERE o.user_id = 4) as total_order_items;

-- 7. 检查是否有任何订单项数据
SELECT 'All order_items data:' as step;
SELECT COUNT(*) as total_order_items FROM order_items;

-- 8. 如果有数据，显示前几条
SELECT 'Sample order_items:' as step;
SELECT * FROM order_items LIMIT 5;

-- 9. 检查外键约束
SELECT 'Foreign key constraints:' as step;
SELECT 
  CONSTRAINT_NAME,
  COLUMN_NAME,
  REFERENCED_TABLE_NAME,
  REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = 'mall' 
  AND TABLE_NAME = 'order_items'
  AND REFERENCED_TABLE_NAME IS NOT NULL;
