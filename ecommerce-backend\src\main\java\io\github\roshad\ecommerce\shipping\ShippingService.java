package io.github.roshad.ecommerce.shipping;

import java.util.List;

public interface ShippingService {
    
    /**
     * 創建發貨信息
     * @param orderId 訂單ID
     * @param shippingRequest 發貨請求
     * @return 發貨信息
     */
    ShippingInfo createShipping(Long orderId, ShippingRequest shippingRequest);
    
    /**
     * 根據訂單ID獲取發貨信息
     * @param orderId 訂單ID
     * @return 發貨信息
     */
    ShippingInfo getShippingByOrderId(Long orderId);
    
    /**
     * 根據運單號獲取發貨信息
     * @param trackingNumber 運單號
     * @return 發貨信息
     */
    ShippingInfo getShippingByTrackingNumber(String trackingNumber);
    
    /**
     * 更新發貨信息
     * @param shippingId 發貨信息ID
     * @param shippingRequest 發貨請求
     * @return 更新後的發貨信息
     */
    ShippingInfo updateShipping(Long shippingId, ShippingRequest shippingRequest);
    
    /**
     * 獲取所有發貨信息
     * @return 發貨信息列表
     */
    List<ShippingInfo> getAllShippings();
    
    /**
     * 根據快遞公司獲取發貨信息
     * @param carrier 快遞公司
     * @return 發貨信息列表
     */
    List<ShippingInfo> getShippingsByCarrier(String carrier);
    
    /**
     * 檢查訂單是否可以發貨
     * @param orderId 訂單ID
     * @return 是否可以發貨
     */
    boolean canShipOrder(Long orderId);
}
