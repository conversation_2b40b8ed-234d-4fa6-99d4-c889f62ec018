'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { EyeIcon, ClockIcon, CheckCircleIcon, TruckIcon } from '@heroicons/react/24/outline';
import { useOrderStore, initializeOrderStore } from '../../store/orderStore';
import { OrderStatus } from '../../types/order';

export default function OrdersPage() {
  const { orders, getUserOrders, getOrdersByStatus, isLoading } = useOrderStore();
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus | 'ALL'>('ALL');

  // 页面加载时初始化订单数据
  useEffect(() => {
    initializeOrderStore(); // 初始化并从后端加载当前用户的订单数据
  }, []);

  // 获取过滤后的订单
  const filteredOrders = selectedStatus === 'ALL' 
    ? getUserOrders() 
    : getOrdersByStatus(selectedStatus);

  // 状态过滤选项
  const statusFilters = [
    { key: 'ALL', label: '全部', count: orders.length },
    { key: OrderStatus.PENDING, label: '待付款', count: getOrdersByStatus(OrderStatus.PENDING).length },
    { key: OrderStatus.PAID, label: '已付款', count: getOrdersByStatus(OrderStatus.PAID).length },
    { key: OrderStatus.SHIPPED, label: '已发货', count: getOrdersByStatus(OrderStatus.SHIPPED).length },
    { key: OrderStatus.DELIVERED, label: '已送达', count: getOrdersByStatus(OrderStatus.DELIVERED).length },
    { key: OrderStatus.CANCELLED, label: '已取消', count: getOrdersByStatus(OrderStatus.CANCELLED).length },
  ];

  // 获取订单状态信息
  const getStatusInfo = (status: OrderStatus) => {
    switch (status) {
      case OrderStatus.PENDING:
        return { text: '待付款', color: 'text-orange-600', bgColor: 'bg-orange-100', icon: ClockIcon };
      case OrderStatus.PAID:
        return { text: '已付款', color: 'text-blue-600', bgColor: 'bg-blue-100', icon: CheckCircleIcon };
      case OrderStatus.PROCESSING:
        return { text: '处理中', color: 'text-blue-600', bgColor: 'bg-blue-100', icon: ClockIcon };
      case OrderStatus.SHIPPED:
        return { text: '已发货', color: 'text-purple-600', bgColor: 'bg-purple-100', icon: TruckIcon };
      case OrderStatus.DELIVERED:
        return { text: '已送达', color: 'text-green-600', bgColor: 'bg-green-100', icon: CheckCircleIcon };
      case OrderStatus.CANCELLED:
        return { text: '已取消', color: 'text-red-600', bgColor: 'bg-red-100', icon: ClockIcon };
      default:
        return { text: '未知', color: 'text-gray-600', bgColor: 'bg-gray-100', icon: ClockIcon };
    }
  };

  // 处理图片URL
  const getImageUrl = (url: string, productName: string) => {
    if (!url) return '/placeholder.jpg';
    
    if (url.startsWith('/images/')) {
      return `https://via.placeholder.com/60x60/f0f0f0/666666?text=${encodeURIComponent(productName)}`;
    }
    
    try {
      new URL(url);
      return url;
    } catch {
      return `https://via.placeholder.com/60x60/f0f0f0/666666?text=${encodeURIComponent(productName)}`;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">我的订单</h1>
        <p className="text-gray-600 mt-1">查看和管理您的所有订单</p>
      </div>

      {/* 状态过滤 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
        <div className="flex flex-wrap gap-2">
          {statusFilters.map((filter) => (
            <button
              key={filter.key}
              onClick={() => setSelectedStatus(filter.key as OrderStatus | 'ALL')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedStatus === filter.key
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {filter.label}
              {filter.count > 0 && (
                <span className={`ml-1 ${
                  selectedStatus === filter.key ? 'text-blue-200' : 'text-gray-500'
                }`}>
                  ({filter.count})
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* 订单列表 */}
      {isLoading ? (
        <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
          <div className="text-gray-400 mb-4">
            <ClockIcon className="w-16 h-16 mx-auto animate-spin" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">加载中...</h3>
          <p className="text-gray-500">正在获取您的订单信息</p>
        </div>
      ) : filteredOrders.length === 0 ? (
        <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
          <div className="text-gray-400 mb-4">
            <ClockIcon className="w-16 h-16 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无订单</h3>
          <p className="text-gray-600 mb-6">
            {selectedStatus === 'ALL' ? '您还没有任何订单' : `暂无${statusFilters.find(f => f.key === selectedStatus)?.label}订单`}
          </p>
          <Link
            href="/"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            去购物
          </Link>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredOrders.map((order) => {
            const statusInfo = getStatusInfo(order.status);
            const StatusIcon = statusInfo.icon;
            
            return (
              <div key={order.id} className="bg-white rounded-lg border border-gray-200 p-6">
                {/* 订单头部 */}
                <div className="flex items-center justify-between mb-4 pb-4 border-b border-gray-100">
                  <div className="flex items-center space-x-4">
                    <div>
                      <p className="text-sm text-gray-600">订单号</p>
                      <p className="font-medium text-gray-900">{order.orderNumber}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">下单时间</p>
                      <p className="font-medium text-gray-900">
                        {new Date(order.createdAt).toLocaleDateString('zh-CN')}
                      </p>
                    </div>
                  </div>
                  
                  <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${statusInfo.bgColor}`}>
                    <StatusIcon className={`w-4 h-4 ${statusInfo.color}`} />
                    <span className={`text-sm font-medium ${statusInfo.color}`}>
                      {statusInfo.text}
                    </span>
                  </div>
                </div>

                {/* 商品列表 */}
                <div className="space-y-3 mb-4">
                  {order.items.slice(0, 3).map((item) => (
                    <div key={item.id} className="flex items-center space-x-3">
                      <div className="relative w-12 h-12 flex-shrink-0">
                        <Image
                          src={getImageUrl(item.productImage, item.productName)}
                          alt={item.productName}
                          fill
                          style={{ objectFit: "cover" }}
                          className="rounded-md"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 line-clamp-1">
                          {item.productName}
                        </p>
                        <p className="text-xs text-gray-500">
                          ¥{item.price.toFixed(2)} × {item.quantity}
                        </p>
                      </div>
                      <div className="text-sm font-medium text-gray-900">
                        ¥{item.subtotal.toFixed(2)}
                      </div>
                    </div>
                  ))}
                  
                  {order.items.length > 3 && (
                    <p className="text-sm text-gray-500 text-center">
                      还有 {order.items.length - 3} 件商品...
                    </p>
                  )}
                </div>

                {/* 订单底部 */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                  <div className="text-sm text-gray-600">
                    共 {order.items.reduce((sum, item) => sum + item.quantity, 0)} 件商品
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="text-sm text-gray-600">订单总额</p>
                      <p className="text-lg font-semibold text-red-600">
                        ¥{order.totalAmount.toFixed(2)}
                      </p>
                    </div>

                    <div className="flex space-x-2">
                      <Link
                        href={`/orders/${order.id}`}
                        className="flex items-center space-x-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        <EyeIcon className="w-4 h-4" />
                        <span>查看详情</span>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* 返回购物按钮 */}
      {filteredOrders.length > 0 && (
        <div className="mt-8 text-center">
          <Link
            href="/"
            className="inline-flex items-center px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            继续购物
          </Link>
        </div>
      )}
    </div>
  );
}
