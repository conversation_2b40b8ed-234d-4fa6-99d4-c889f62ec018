'use client';

import React from 'react';
import Image from 'next/image';
import { useCartStore } from '../../store/cartStore';

interface OrderSummaryProps {
  showItems?: boolean;
  className?: string;
}

const OrderSummary: React.FC<OrderSummaryProps> = ({ 
  showItems = true, 
  className = '' 
}) => {
  const { items, totalItems, totalPrice } = useCartStore();

  // 計算運費
  const shippingFee = totalPrice >= 99 ? 0 : 10;
  const finalTotal = totalPrice + shippingFee;

  // 處理圖片URL
  const getImageUrl = (url: string, productName: string) => {
    if (!url) return '/placeholder.jpg';
    
    if (url.startsWith('/images/')) {
      return `https://via.placeholder.com/80x80/f0f0f0/666666?text=${encodeURIComponent(productName)}`;
    }
    
    try {
      new URL(url);
      return url;
    } catch {
      return `https://via.placeholder.com/80x80/f0f0f0/666666?text=${encodeURIComponent(productName)}`;
    }
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">訂單摘要</h3>
      
      {/* 商品列表 */}
      {showItems && items.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">
            商品清單 ({totalItems} 件)
          </h4>
          <div className="space-y-3 max-h-60 overflow-y-auto">
            {items.map((item) => (
              <div key={item.id} className="flex items-center space-x-3">
                <div className="relative w-12 h-12 flex-shrink-0">
                  <Image
                    src={getImageUrl(item.imageUrl, item.name)}
                    alt={item.name}
                    fill
                    style={{ objectFit: "cover" }}
                    className="rounded-md"
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 line-clamp-1">
                    {item.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    ¥{item.price.toFixed(2)} × {item.quantity}
                  </p>
                </div>
                <div className="text-sm font-medium text-gray-900">
                  ¥{(item.price * item.quantity).toFixed(2)}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* 價格明細 */}
      <div className="space-y-3 border-t border-gray-200 pt-4">
        <div className="flex justify-between text-gray-600">
          <span>商品小計</span>
          <span>¥{totalPrice.toFixed(2)}</span>
        </div>
        
        <div className="flex justify-between text-gray-600">
          <span>運費</span>
          <span className={shippingFee === 0 ? 'text-green-600' : ''}>
            {shippingFee === 0 ? '免費' : `¥${shippingFee.toFixed(2)}`}
          </span>
        </div>
        
        {/* 免運費提示 */}
        {shippingFee > 0 && (
          <div className="text-xs text-orange-600 bg-orange-50 p-2 rounded">
            再購買 ¥{(99 - totalPrice).toFixed(2)} 即可享受免運費
          </div>
        )}
        
        {shippingFee === 0 && totalPrice >= 99 && (
          <div className="text-xs text-green-600 bg-green-50 p-2 rounded">
            🎉 恭喜！您已享受免運費優惠
          </div>
        )}
        
        {/* 優惠券 */}
        <div className="flex justify-between text-gray-600">
          <span>優惠券</span>
          <span className="text-green-600">-¥0.00</span>
        </div>
        
        {/* 總計 */}
        <div className="flex justify-between text-lg font-semibold text-gray-900 border-t border-gray-200 pt-3">
          <span>總計</span>
          <span className="text-red-600">¥{finalTotal.toFixed(2)}</span>
        </div>
      </div>
      
      {/* 優惠信息 */}
      <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start space-x-2">
          <span className="text-blue-600 text-sm">💡</span>
          <div className="text-blue-700 text-xs">
            <p className="font-medium">購物提示</p>
            <ul className="mt-1 space-y-1">
              <li>• 滿99元免運費</li>
              <li>• 支持7天無理由退換貨</li>
              <li>• 正品保證，假一賠十</li>
            </ul>
          </div>
        </div>
      </div>
      
      {/* 空購物車提示 */}
      {items.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500 mb-2">購物車為空</p>
          <p className="text-sm text-gray-400">
            請先添加商品到購物車
          </p>
        </div>
      )}
    </div>
  );
};

export default OrderSummary;
