package io.github.roshad.ecommerce.shipping;

import io.github.roshad.ecommerce.order.Order;
import io.github.roshad.ecommerce.order.OrderService;
import io.github.roshad.ecommerce.order.OrderStatus;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ShippingServiceImpl implements ShippingService {
    
    private final ShippingMapper shippingMapper;
    private final OrderService orderService;
    
    @Override
    @Transactional
    public ShippingInfo createShipping(Long orderId, ShippingRequest shippingRequest) {
        // 檢查訂單是否可以發貨
        if (!canShipOrder(orderId)) {
            throw new IllegalStateException("訂單狀態不允許發貨");
        }
        
        // 檢查是否已經有發貨信息
        ShippingInfo existingShipping = shippingMapper.findByOrderId(orderId);
        if (existingShipping != null) {
            throw new IllegalStateException("該訂單已經發貨");
        }
        
        // 創建發貨信息
        ShippingInfo shippingInfo = new ShippingInfo();
        shippingInfo.setOrderId(orderId);
        shippingInfo.setTrackingNumber(shippingRequest.getTrackingNumber());
        shippingInfo.setCarrier(shippingRequest.getCarrier());
        shippingInfo.setShippingMethod(shippingRequest.getShippingMethod());
        shippingInfo.setShippedAt(LocalDateTime.now());
        shippingInfo.setNotes(shippingRequest.getNotes());
        shippingInfo.setCreatedAt(LocalDateTime.now());
        shippingInfo.setUpdatedAt(LocalDateTime.now());
        
        // 計算預計送達時間
        if (shippingRequest.getEstimatedDays() != null && shippingRequest.getEstimatedDays() > 0) {
            shippingInfo.setEstimatedDelivery(
                LocalDateTime.now().plusDays(shippingRequest.getEstimatedDays())
            );
        }
        
        // 保存發貨信息
        shippingMapper.insert(shippingInfo);
        
        // 更新訂單狀態為已發貨
        orderService.updateOrderStatus(orderId, OrderStatus.SHIPPED);
        
        return shippingInfo;
    }
    
    @Override
    public ShippingInfo getShippingByOrderId(Long orderId) {
        return shippingMapper.findByOrderId(orderId);
    }
    
    @Override
    public ShippingInfo getShippingByTrackingNumber(String trackingNumber) {
        return shippingMapper.findByTrackingNumber(trackingNumber);
    }
    
    @Override
    @Transactional
    public ShippingInfo updateShipping(Long shippingId, ShippingRequest shippingRequest) {
        ShippingInfo shippingInfo = shippingMapper.findById(shippingId);
        if (shippingInfo == null) {
            throw new IllegalArgumentException("發貨信息不存在");
        }
        
        // 更新發貨信息
        shippingInfo.setTrackingNumber(shippingRequest.getTrackingNumber());
        shippingInfo.setCarrier(shippingRequest.getCarrier());
        shippingInfo.setShippingMethod(shippingRequest.getShippingMethod());
        shippingInfo.setNotes(shippingRequest.getNotes());
        shippingInfo.setUpdatedAt(LocalDateTime.now());
        
        // 重新計算預計送達時間
        if (shippingRequest.getEstimatedDays() != null && shippingRequest.getEstimatedDays() > 0) {
            shippingInfo.setEstimatedDelivery(
                shippingInfo.getShippedAt().plusDays(shippingRequest.getEstimatedDays())
            );
        }
        
        shippingMapper.update(shippingInfo);
        return shippingInfo;
    }
    
    @Override
    public List<ShippingInfo> getAllShippings() {
        return shippingMapper.findAll();
    }
    
    @Override
    public List<ShippingInfo> getShippingsByCarrier(String carrier) {
        return shippingMapper.findByCarrier(carrier);
    }
    
    @Override
    public boolean canShipOrder(Long orderId) {
        Order order = orderService.getOrderById(orderId);
        if (order == null) {
            return false;
        }
        
        // 只有已付款或處理中的訂單可以發貨
        String statusName = order.getStatus().getStatusName();
        return OrderStatus.PAID.equals(statusName) || OrderStatus.PROCESSING.equals(statusName);
    }
}
