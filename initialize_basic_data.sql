-- 初始化基础数据脚本
USE mall;

-- 1. 初始化角色数据
INSERT IGNORE INTO roles (name, description) VALUES
('USER', '普通用户，可以浏览商品、下单购买'),
('ADMIN', '管理员，拥有系统管理权限'),
('SELLER', '商家，可以管理自己的商品'),
('MODERATOR', '版主，可以管理内容和用户');

-- 2. 初始化订单状态数据
INSERT IGNORE INTO order_statuses (status_name, description) VALUES
('PENDING', '待付款 - 订单已创建，等待付款'),
('PAID', '已付款 - 订单已付款，等待处理'),
('PROCESSING', '处理中 - 订单正在处理中'),
('SHIPPED', '已发货 - 订单已发货，正在运输中'),
('DELIVERED', '已送达 - 订单已成功送达'),
('CANCELLED', '已取消 - 订单已被取消'),
('REFUNDED', '已退款 - 订单已退款');

-- 3. 为现有用户分配默认角色（如果有用户的话）
-- 首先检查是否有用户没有角色
INSERT IGNORE INTO user_roles (user_id, role_id)
SELECT u.id, r.id
FROM users u
CROSS JOIN roles r
WHERE r.name = 'USER'
AND NOT EXISTS (
    SELECT 1 FROM user_roles ur WHERE ur.user_id = u.id
);

-- 4. 检查结果
SELECT 'Roles initialized:' as message;
SELECT * FROM roles;

SELECT 'Order statuses initialized:' as message;
SELECT * FROM order_statuses;

SELECT 'User roles assigned:' as message;
SELECT u.username, r.name as role_name
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id;
