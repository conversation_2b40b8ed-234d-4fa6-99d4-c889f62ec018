-- 创建订单项表（如果不存在）
USE mall;

-- 检查当前数据库中的表
SELECT 'Current tables in database:' as message;
SHOW TABLES;

-- 检查 order_items 表是否存在
SELECT 'Checking if order_items table exists:' as message;
SELECT COUNT(*) as table_exists
FROM information_schema.tables
WHERE table_schema = 'mall' AND table_name = 'order_items';

-- 创建 order_items 表
CREATE TABLE IF NOT EXISTS `order_items` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `order_id` BIGINT NOT NULL,
  `product_id` BIGINT NOT NULL,
  `quantity` INT NOT NULL DEFAULT 1,
  `price_at_purchase` DECIMAL(10, 2) NOT NULL,
  `product_name_at_purchase` VARCHAR(255) NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  -- 索引
  INDEX `idx_order_items_order_id` (`order_id`),
  INDEX `idx_order_items_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 检查表是否创建成功
SELECT 'order_items table structure:' as message;
DESCRIBE order_items;

-- 检查现有数据
SELECT 'Current order_items count:' as message;
SELECT COUNT(*) as count FROM order_items;

-- 检查最近的订单数据
SELECT 'Recent orders:' as message;
SELECT id, order_code, total_amount, created_at FROM orders ORDER BY created_at DESC LIMIT 5;

-- 检查是否有对应的订单项
SELECT 'Order items for recent orders:' as message;
SELECT oi.*, o.order_code
FROM order_items oi
JOIN orders o ON oi.order_id = o.id
ORDER BY oi.created_at DESC LIMIT 10;
