package io.github.roshad.ecommerce.order;

import io.github.roshad.ecommerce.shipping.ShippingInfo;
import io.github.roshad.ecommerce.shipping.ShippingMapper;
import io.github.roshad.ecommerce.shipping.ShippingRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class OrderServiceImpl implements OrderService {
    private final OrderMapper orderMapper;
    private final OrderItemMapper orderItemMapper;
    private final OrderStatusService orderStatusService;
    private final io.github.roshad.ecommerce.auth.UserService userService;
    private final ShippingMapper shippingMapper;

    @Override
    @Transactional
    public Order createOrder(Order order) {
        // 设置默认状态为PENDING
        OrderStatus pendingStatus = orderStatusService.findByStatusName(OrderStatus.PENDING);
        order.setStatus(pendingStatus);
        order.setStatusId(pendingStatus.getId());
        order.setCreatedAt(LocalDateTime.now());
        order.setUpdatedAt(LocalDateTime.now());

        // 保存订单主表
        orderMapper.insert(order);

        // 保存订单项
        if (order.getOrderItems() != null && !order.getOrderItems().isEmpty()) {
            for (OrderItem item : order.getOrderItems()) {
                item.setOrderId(order.getId());
                orderItemMapper.insert(item);
            }
        }

        return order;
    }

    @Override
    public Order getOrderById(Long id) {
        return orderMapper.findById(id);
    }

    @Override
    public List<Order> getOrdersByUserId(Long userId) {
        return orderMapper.findByUserId(userId);
    }

    @Override
    public void updateOrderStatus(Long orderId, String statusName) {
        Order order = orderMapper.findById(orderId);
        if (order != null) {
            OrderStatus newStatus = orderStatusService.findByStatusName(statusName);
            if (newStatus != null) {
                order.setStatus(newStatus);
                order.setStatusId(newStatus.getId());
                order.setUpdatedAt(LocalDateTime.now());
                orderMapper.update(order);
            }
        }
    }

    @Override
    public void cancelOrder(Long orderId) {
        updateOrderStatus(orderId, OrderStatus.CANCELLED);
    }
    @Override
    public boolean isOrderOwner(Long orderId, String username) {
        Order order = orderMapper.findById(orderId);
        if (order == null) {
            return false;
        }
        Long userId = userService.getUserId(username);
        return order.getUserId().equals(userId);
    }

    @Override
    public List<Order> getAllOrders() {
        return orderMapper.findAll();
    }

    // 发货相关方法实现
    @Override
    @Transactional
    public ShippingInfo shipOrder(Long orderId, ShippingRequest shippingRequest) {
        // 检查订单是否可以发货
        if (!canShipOrder(orderId)) {
            throw new IllegalStateException("订单状态不允许发货");
        }

        // 检查是否已经有发货信息
        ShippingInfo existingShipping = shippingMapper.findByOrderId(orderId);
        if (existingShipping != null) {
            throw new IllegalStateException("该订单已经发货");
        }

        // 创建发货信息
        ShippingInfo shippingInfo = new ShippingInfo();
        shippingInfo.setOrderId(orderId);
        shippingInfo.setTrackingNumber(shippingRequest.getTrackingNumber());
        shippingInfo.setCarrier(shippingRequest.getCarrier());
        shippingInfo.setShippingMethod(shippingRequest.getShippingMethod());
        shippingInfo.setShippedAt(LocalDateTime.now());
        shippingInfo.setNotes(shippingRequest.getNotes());
        shippingInfo.setCreatedAt(LocalDateTime.now());
        shippingInfo.setUpdatedAt(LocalDateTime.now());

        // 计算预计送达时间
        if (shippingRequest.getEstimatedDays() != null && shippingRequest.getEstimatedDays() > 0) {
            shippingInfo.setEstimatedDelivery(
                LocalDateTime.now().plusDays(shippingRequest.getEstimatedDays())
            );
        }

        // 保存发货信息
        shippingMapper.insert(shippingInfo);

        // 更新订单状态为已发货
        updateOrderStatus(orderId, OrderStatus.SHIPPED);

        return shippingInfo;
    }

    @Override
    public ShippingInfo getOrderShipping(Long orderId) {
        return shippingMapper.findByOrderId(orderId);
    }

    @Override
    @Transactional
    public ShippingInfo updateOrderShipping(Long orderId, ShippingRequest shippingRequest) {
        ShippingInfo shippingInfo = shippingMapper.findByOrderId(orderId);
        if (shippingInfo == null) {
            throw new IllegalArgumentException("该订单尚未发货");
        }

        // 更新发货信息
        shippingInfo.setTrackingNumber(shippingRequest.getTrackingNumber());
        shippingInfo.setCarrier(shippingRequest.getCarrier());
        shippingInfo.setShippingMethod(shippingRequest.getShippingMethod());
        shippingInfo.setNotes(shippingRequest.getNotes());
        shippingInfo.setUpdatedAt(LocalDateTime.now());

        // 重新计算预计送达时间
        if (shippingRequest.getEstimatedDays() != null && shippingRequest.getEstimatedDays() > 0) {
            shippingInfo.setEstimatedDelivery(
                shippingInfo.getShippedAt().plusDays(shippingRequest.getEstimatedDays())
            );
        }

        shippingMapper.update(shippingInfo);
        return shippingInfo;
    }

    @Override
    public List<Order> getOrdersReadyToShip() {
        List<Order> allOrders = orderMapper.findAll();
        return allOrders.stream()
            .filter(order -> canShipOrder(order.getId()))
            .collect(Collectors.toList());
    }

    @Override
    public boolean canShipOrder(Long orderId) {
        Order order = getOrderById(orderId);
        if (order == null) {
            return false;
        }

        // 检查是否已经发货
        ShippingInfo existingShipping = shippingMapper.findByOrderId(orderId);
        if (existingShipping != null) {
            return false; // 已经发货的订单不能再次发货
        }

        // 只有已付款或处理中的订单可以发货
        String statusName = order.getStatus().getStatusName();
        return OrderStatus.PAID.equals(statusName) || OrderStatus.PROCESSING.equals(statusName);
    }
}